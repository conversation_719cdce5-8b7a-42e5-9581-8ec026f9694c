import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:provider/provider.dart';
import '../theme/app_theme.dart';
import 'main_navigation.dart';
import '../providers/app_state.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _completeOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('onboarding_completed', true);

    if (mounted) {
      Navigator.pushReplacement(
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              const MainNavigation(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
          transitionDuration: const Duration(milliseconds: 800),
        ),
      );
    }
  }

  void _nextPage() {
    if (_currentPage < 4) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }

  void _skipOnboarding() {
    _completeOnboarding();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF87CEEB), Colors.white],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Skip button
              Align(
                alignment: Alignment.topRight,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: TextButton(
                    onPressed: _skipOnboarding,
                    child: Text(
                      'Skip',
                      style: GoogleFonts.lato(
                        fontSize: 16,
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),

              // Page content
              Expanded(
                child: PageView(
                  controller: _pageController,
                  onPageChanged: (index) {
                    setState(() {
                      _currentPage = index;
                    });
                  },
                  children: [
                    _buildPage1(),
                    _buildPage2(),
                    _buildPage3(),
                    _buildPage4(),
                    _buildPage5(),
                  ],
                ),
              ),

              // Page indicators
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 20.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    5,
                    (index) => Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4.0),
                      width: _currentPage == index ? 12.0 : 8.0,
                      height: 8.0,
                      decoration: BoxDecoration(
                        color: _currentPage == index
                            ? AppTheme.primaryColor
                            : AppTheme.primaryColor
                                .withAlpha((0.3 * 255).toInt()),
                        borderRadius: BorderRadius.circular(4.0),
                      ),
                    ),
                  ),
                ),
              ),

              // Navigation button
              Padding(
                padding: const EdgeInsets.all(24.0),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _nextPage,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      _currentPage == 3 ? 'Get Started' : 'Next',
                      style: GoogleFonts.lato(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Page 1 - Welcome & Core Benefit
  Widget _buildPage1() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Visual: App logo
          Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withAlpha((0.1 * 255).toInt()),
              shape: BoxShape.circle,
              border: Border.all(
                color: AppTheme.primaryColor,
                width: 3,
              ),
            ),
            child: ClipOval(
              child: Padding(
                padding: const EdgeInsets.all(30.0),
                child: Image.asset(
                  'assets/images/icon/icon_app.webp',
                  width: 140,
                  height: 140,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return const Icon(
                      FeatherIcons.wind,
                      size: 100,
                      color: AppTheme.primaryColor,
                    );
                  },
                ),
              ),
            ),
          ),
          const SizedBox(height: 60),

          // Title
          Text(
            'به تن‌آرام خوش آمدید!',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: AppTheme.textColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // Description
          Text(
            'قدرت نفس خود را کشف کنید. استرس را کاهش دهید، تمرکز را بهبود بخشید و سلامت خود را با تمرینات تنفسی هدایت شده ارتقا دهید.',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 18,
              color: AppTheme.lightTextColor,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Page 2 - Breathing Techniques
  Widget _buildPage2() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Visual: Three technique icons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildTechniqueIcon(
                FeatherIcons.moon,
                Colors.indigo,
                'خواب',
              ),
              _buildTechniqueIcon(
                FeatherIcons.zap,
                Colors.orange,
                'انرژی',
              ),
              _buildTechniqueIcon(
                FeatherIcons.target,
                Colors.teal,
                'تمرکز',
              ),
            ],
          ),
          const SizedBox(height: 60),

          // Title
          Text(
            'آرامش، انرژی یا تمرکز خود را بیابید',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: AppTheme.textColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // Description
          Text(
            'انواع تکنیک‌های تنفسی متناسب با نیازهای شما را کاوش کنید - از تمرینات آرام‌بخش برای استراحت و خواب تا تنفس‌های انرژی‌بخش برای تقویت سریع. تکنیک‌های جدید با پیشرفت شما باز می‌شوند!',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 18,
              color: AppTheme.lightTextColor,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Helper method for technique icons
  Widget _buildTechniqueIcon(IconData icon, Color color, String label) {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: color.withAlpha((0.2 * 255).toInt()),
            shape: BoxShape.circle,
            border: Border.all(
              color: color,
              width: 2,
            ),
          ),
          child: Icon(
            icon,
            size: 40,
            color: color,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: GoogleFonts.lato(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppTheme.textColor,
          ),
        ),
      ],
    );
  }

  // Page 3 - Progress Tracking
  Widget _buildPage3() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Visual: Progress icons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildProgressIcon(
                FeatherIcons.star,
                AppTheme.primaryColor,
                'Points',
              ),
              _buildProgressIcon(
                FeatherIcons.trendingUp,
                Colors.green,
                'Progress',
              ),
              _buildProgressIcon(
                FeatherIcons.award,
                Colors.amber,
                'Achievements',
              ),
            ],
          ),
          const SizedBox(height: 40),

          // Sample chart preview
          _buildSampleChart(),
          const SizedBox(height: 40),

          // Title
          Text(
            'رشد کنید و پیشرفت خود را ببینید',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: AppTheme.textColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // Description
          Text(
            'امتیاز کسب کنید، سطح خود را بالا ببرید و چالش‌ها را تکمیل کنید. حال و هوای خود را پس از هر جلسه ردیابی کنید و ببینید که سلامت شما با گذشت زمان چگونه بهبود می‌یابد.',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 18,
              color: AppTheme.lightTextColor,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Helper method for progress icons
  Widget _buildProgressIcon(IconData icon, Color color, String label) {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: color.withAlpha((0.2 * 255).toInt()),
            shape: BoxShape.circle,
            border: Border.all(
              color: color,
              width: 2,
            ),
          ),
          child: Icon(
            icon,
            size: 40,
            color: color,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: GoogleFonts.lato(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppTheme.textColor,
          ),
        ),
      ],
    );
  }

  // Page 4 - Personalization
  Widget _buildPage4() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Visual: All profile avatars showcase
          Column(
            children: [
              // Top row of profiles
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildProfileAvatar(
                      'assets/images/profiles/profile_level1.webp', 'مبتدی'),
                  _buildProfileAvatar(
                      'assets/images/profiles/profile_level2.webp', 'متوسط'),
                ],
              ),
              const SizedBox(height: 20),
              // Bottom row of profiles
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildProfileAvatar(
                      'assets/images/profiles/profile_level3.webp', 'پیشرفته'),
                  _buildProfileAvatar(
                      'assets/images/profiles/profile_level4.webp', 'استاد'),
                ],
              ),
            ],
          ),
          const SizedBox(height: 60),

          // Title
          Text(
            'آن را متعلق به خود کنید',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: AppTheme.textColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // Description
          Text(
            'تمرین خود را با موسیقی پس‌زمینه شخصی‌سازی کنید، یادآوری‌های روزانه تنظیم کنید تا عادت ثابتی ایجاد کنید و با رسیدن به سطوح جدید، آواتارهای پروفایل جدید را باز کنید.',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 18,
              color: AppTheme.lightTextColor,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Helper method for profile avatars
  Widget _buildProfileAvatar(String imagePath, String label) {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withAlpha((0.1 * 255).toInt()),
            shape: BoxShape.circle,
            border: Border.all(
              color: AppTheme.primaryColor,
              width: 2,
            ),
          ),
          child: ClipOval(
            child: Image.asset(
              imagePath,
              width: 80,
              height: 80,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return const Icon(
                  FeatherIcons.user,
                  size: 40,
                  color: AppTheme.primaryColor,
                );
              },
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: const TextStyle(
            fontFamily: 'Samim',
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: AppTheme.textColor,
          ),
        ),
      ],
    );
  }

  // Helper method for sample chart
  Widget _buildSampleChart() {
    return Container(
      height: 120,
      width: 280,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha((0.1 * 255).toInt()),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'پیشرفت حال و هوا',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppTheme.textColor,
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: LineChart(
              LineChartData(
                gridData: const FlGridData(show: false),
                titlesData: const FlTitlesData(show: false),
                borderData: FlBorderData(show: false),
                lineBarsData: [
                  LineChartBarData(
                    spots: const [
                      FlSpot(0, 3),
                      FlSpot(1, 3.5),
                      FlSpot(2, 3.2),
                      FlSpot(3, 4.1),
                      FlSpot(4, 4.5),
                      FlSpot(5, 4.2),
                      FlSpot(6, 4.8),
                    ],
                    isCurved: true,
                    color: AppTheme.primaryColor,
                    barWidth: 3,
                    dotData: FlDotData(
                      show: true,
                      getDotPainter: (spot, percent, barData, index) {
                        return FlDotCirclePainter(
                          radius: 4,
                          color: AppTheme.primaryColor,
                          strokeWidth: 2,
                          strokeColor: Colors.white,
                        );
                      },
                    ),
                    belowBarData: BarAreaData(
                      show: true,
                      color:
                          AppTheme.primaryColor.withAlpha((0.1 * 255).toInt()),
                    ),
                  ),
                ],
                minX: 0,
                maxX: 6,
                minY: 1,
                maxY: 5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Page 5 - Notification Permission
  Widget _buildPage5() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Visual: Notification icon
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withAlpha((0.1 * 255).toInt()),
              shape: BoxShape.circle,
              border: Border.all(
                color: AppTheme.primaryColor,
                width: 3,
              ),
            ),
            child: const Icon(
              FeatherIcons.bell,
              size: 60,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 60),

          // Title
          Text(
            'در مسیر باقی بمانید',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: AppTheme.textColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // Description
          Text(
            'یادآوری‌های ملایم برای تمرین تنفسی دریافت کنید و یک روتین سلامت ثابت ایجاد کنید.',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 18,
              color: AppTheme.lightTextColor,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 40),

          // Permission request button
          Consumer<AppState>(
            builder: (context, appState, child) {
              return Column(
                children: [
                  ElevatedButton(
                    onPressed: () async {
                      final scaffoldMessenger = ScaffoldMessenger.of(context);
                      final success =
                          await appState.requestNotificationPermissions();
                      if (success) {
                        // Permission granted, show success message
                        if (mounted) {
                          scaffoldMessenger.showSnackBar(
                            const SnackBar(
                              content: Text(
                                'عالی! شما یادآوری‌های مفید دریافت خواهید کرد.',
                                style: TextStyle(fontFamily: 'Samim'),
                              ),
                              backgroundColor: Colors.green,
                              duration: Duration(seconds: 2),
                            ),
                          );
                        }
                      }
                      // Continue to next step regardless of permission result
                      await Future.delayed(const Duration(milliseconds: 500));
                      _completeOnboarding();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 32, vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                    ),
                    child: Text(
                      'فعال‌سازی یادآوری‌ها',
                      style: const TextStyle(
                        fontFamily: 'Samim',
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextButton(
                    onPressed: _completeOnboarding,
                    child: Text(
                      'شاید بعداً',
                      style: const TextStyle(
                        fontFamily: 'Samim',
                        fontSize: 14,
                        color: AppTheme.lightTextColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}
