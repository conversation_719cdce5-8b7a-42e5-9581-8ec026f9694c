import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../utils/persian_utils.dart';
import '../providers/app_state.dart';
import '../theme/app_theme.dart';
import '../widgets/points_badge.dart';
import '../widgets/technique_unlock_dialog.dart';
import '../models/exercise_history.dart';
import 'technique_detail_screen.dart';
import 'exercise_history_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // Check for newly unlocked techniques after widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkForUnlockedTechniques();
    });
  }

  void _checkForUnlockedTechniques() {
    final appState = Provider.of<AppState>(context, listen: false);

    // Check if there are newly unlocked techniques
    if (appState.newlyUnlockedTechniques.isNotEmpty) {
      // Show the unlock dialog
      showDialog(
        context: context,
        builder: (context) => TechniqueUnlockDialog(
          unlockedTechniques: appState.newlyUnlockedTechniques,
        ),
      ).then((_) {
        // Clear the newly unlocked techniques after dialog is closed
        appState.clearNewlyUnlockedTechniques();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get the screen size to ensure proper layout
    final screenSize = MediaQuery.of(context).size;

    return Scaffold(
      body: Container(
        width: screenSize.width,
        height: screenSize.height,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF87CEEB), Colors.white],
          ),
        ),
        child: SafeArea(
          child: LayoutBuilder(builder: (context, constraints) {
            return SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: constraints.maxHeight,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildAppBar(context),
                    const SizedBox(height: 20),
                    _buildWelcomeSection(context),
                    const SizedBox(height: 20),
                    _buildAllTechniques(context),
                    const SizedBox(height: 40),
                    _buildRecentHistory(context),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            );
          }),
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'تن‌آرام',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
          PointsBadge(points: appState.points),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    final userTitle = appState.getUserTitle();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'خوش آمدید، $userTitle',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2F4F4F),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'امروز چه تمرینی را می‌خواهید انجام دهید؟',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 16,
              color: Color(0xFF333333),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAllTechniques(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    final techniques = appState.breathingTechniques;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Text(
            'همه تکنیک‌ها',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2F4F4F),
            ),
          ),
        ),
        const SizedBox(height: 10),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: GridView.builder(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 16.0,
              mainAxisSpacing: 16.0,
              childAspectRatio: 1.0,
            ),
            itemCount: techniques.length,
            itemBuilder: (context, index) {
              final technique = techniques[index];
              return _buildTechniqueCard(context, technique);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTechniqueCard(
      BuildContext context, BreathingTechnique technique) {
    final appState = Provider.of<AppState>(context);
    final isUnlocked = appState.isTechniqueUnlocked(technique);
    final requiredTitle = appState.getTitleForLevel(technique.requiredLevel);

    IconData getIconData(String name) {
      switch (name) {
        case 'wind':
          return FeatherIcons.wind;
        case 'moon':
          return FeatherIcons.moon;
        case 'square':
          return FeatherIcons.square;
        case 'git-branch':
          return FeatherIcons.gitBranch;
        case 'sunrise':
          return FeatherIcons.sunrise;
        case 'headphones':
          return FeatherIcons.headphones;
        case 'zap':
          return FeatherIcons.zap;
        default:
          return FeatherIcons.activity;
      }
    }

    return Material(
      color: isUnlocked
          ? technique.color
          : technique.color.withAlpha(128), // 0.5 * 255 = 128
      elevation: 2.0,
      borderRadius: BorderRadius.circular(12.0),
      child: InkWell(
        borderRadius: BorderRadius.circular(12.0),
        onTap: () {
          if (isUnlocked) {
            // Navigate to technique detail if unlocked
            Navigator.push(
              context,
              PageRouteBuilder(
                pageBuilder: (context, animation, secondaryAnimation) =>
                    TechniqueDetailScreen(technique: technique),
                transitionsBuilder:
                    (context, animation, secondaryAnimation, child) {
                  return FadeTransition(opacity: animation, child: child);
                },
                transitionDuration: const Duration(milliseconds: 400),
              ),
            );
          } else {
            // Show unlock message if locked
            _showUnlockRequirementsDialog(context, technique, requiredTitle);
          }
        },
        child: Stack(
          children: [
            // Technique content
            Container(
              width: double.infinity,
              height: double.infinity,
              padding:
                  const EdgeInsets.symmetric(horizontal: 12.0, vertical: 16.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Icon(
                    getIconData(technique.iconName),
                    size: 40.0,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    technique.name,
                    textAlign: TextAlign.center,
                    style: GoogleFonts.lato(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 15,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

            // Lock overlay for locked techniques
            if (!isUnlocked)
              Positioned.fill(
                child: Stack(
                  children: [
                    // Semi-transparent overlay that doesn't cover the technique name
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.black.withAlpha(120),
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),

                    // Lock icon in top-left corner
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.black.withAlpha(150),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          FeatherIcons.lock,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),

                    // Unlock text at the bottom
                    Positioned(
                      left: 0,
                      right: 0,
                      bottom: 0,
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.black.withAlpha(150),
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(12.0),
                            bottomRight: Radius.circular(12.0),
                          ),
                        ),
                        child: Text(
                          'نیازمند سطح: $requiredTitle',
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            fontFamily: 'Samim',
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 11,
                          ),
                        ),
                      ),
                    ),

                    // Profile image in the corner
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.white,
                            width: 1.5,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(50),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: ClipOval(
                          child: Image.asset(
                            appState.getProfileImagePathForLevel(
                                technique.requiredLevel),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Show dialog with unlock requirements
  void _showUnlockRequirementsDialog(BuildContext context,
      BreathingTechnique technique, String requiredTitle) {
    final appState = Provider.of<AppState>(context, listen: false);
    final currentTitle = appState.getUserTitle();
    final requiredLevel = technique.requiredLevel;
    final pointsNeeded = (requiredLevel * 100) - appState.points;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppTheme.primaryColor.withAlpha(100),
                    width: 2,
                  ),
                ),
                child: ClipOval(
                  child: Image.asset(
                    appState
                        .getProfileImagePathForLevel(technique.requiredLevel),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تکنیک قفل شده',
                      style: GoogleFonts.lato(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      requiredTitle,
                      style: GoogleFonts.lato(
                        fontWeight: FontWeight.w500,
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(
                FeatherIcons.lock,
                color: AppTheme.primaryColor,
                size: 20,
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${technique.name} در حال حاضر قفل است.',
                style: const TextStyle(
                  fontFamily: 'Samim',
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'برای باز کردن این تکنیک باید به سطح $requiredTitle برسید.',
                style: const TextStyle(
                  fontFamily: 'Samim',
                  fontSize: 15,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'سطح فعلی شما: $currentTitle',
                style: TextStyle(
                  fontFamily: 'Samim',
                  fontSize: 15,
                  color: Colors.grey[700],
                ),
              ),
              const SizedBox(height: 16),
              if (pointsNeeded > 0)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        FeatherIcons.star,
                        color: AppTheme.primaryColor,
                        size: 20,
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: Text(
                          'برای باز کردن این تکنیک به ${PersianUtils.toPersianNumber(pointsNeeded)} امتیاز بیشتر نیاز دارید.',
                          style: const TextStyle(
                            fontFamily: 'Samim',
                            fontSize: 14,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              const SizedBox(height: 8),
              Text(
                'تمرینات تنفسی و چالش‌ها را تکمیل کنید تا امتیاز کسب کرده و سطح خود را بالا ببرید!',
                style: TextStyle(
                  fontFamily: 'Samim',
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'تایید',
                style: GoogleFonts.lato(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildRecentHistory(BuildContext context) {
    final appState = Provider.of<AppState>(context);

    return FutureBuilder<List<ExerciseHistory>>(
      future: appState.getRecentExerciseHistory(limit: 5),
      builder: (context, snapshot) {
        // Show loading indicator while waiting for data
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(20.0),
              child: CircularProgressIndicator(),
            ),
          );
        }

        // Show error message if there's an error
        if (snapshot.hasError) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Text(
                'خطا در بارگذاری فعالیت‌های اخیر',
                style: GoogleFonts.lato(
                  fontSize: 16,
                  color: Colors.red,
                ),
              ),
            ),
          );
        }

        // Get the history data
        final history = snapshot.data ?? [];

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'فعالیت‌های اخیر',
                    style: const TextStyle(
                      fontFamily: 'Samim',
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2F4F4F),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      // Navigate to full history screen
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ExerciseHistoryScreen(),
                        ),
                      );
                    },
                    child: Text(
                      'مشاهده همه',
                      style: const TextStyle(
                        fontFamily: 'Samim',
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 10),

            // Show message if no history
            if (history.isEmpty)
              Padding(
                padding: const EdgeInsets.all(20.0),
                child: Center(
                  child: Column(
                    children: [
                      const Icon(
                        FeatherIcons.activity,
                        size: 40,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 10),
                      Text(
                        'هنوز هیچ تمرین تنفسی تکمیل نشده است',
                        style: TextStyle(
                          fontFamily: 'Samim',
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 5),
                      Text(
                        'یک تمرین تکمیل کنید تا اینجا نمایش داده شود',
                        style: TextStyle(
                          fontFamily: 'Samim',
                          fontSize: 14,
                          color: Colors.grey[500],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              )
            else
              // Use a Column instead of ListView for better initial rendering
              Column(
                children: List.generate(history.length, (index) {
                  final item = history[index];
                  return _buildHistoryItem(
                    context,
                    item.technique,
                    item.date,
                    item.color,
                    item.iconName,
                  );
                }),
              ),
          ],
        );
      },
    );
  }

  Widget _buildHistoryItem(
    BuildContext context,
    String technique,
    DateTime date,
    Color color,
    String iconName,
  ) {
    IconData getIconData(String name) {
      switch (name) {
        case 'wind':
          return FeatherIcons.wind;
        case 'moon':
          return FeatherIcons.moon;
        case 'square':
          return FeatherIcons.square;
        case 'git-branch':
          return FeatherIcons.gitBranch;
        case 'sunrise':
          return FeatherIcons.sunrise;
        case 'headphones':
          return FeatherIcons.headphones;
        case 'zap':
          return FeatherIcons.zap;
        default:
          return FeatherIcons.activity;
      }
    }

    final formatter = DateFormat('MMM d, h:mm a');
    final formattedDate = formatter.format(date);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 * 255 ≈ 13
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withAlpha(51), // 0.2 * 255 ≈ 51
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            getIconData(iconName),
            color: color,
            size: 24,
          ),
        ),
        title: Text(
          technique,
          style: GoogleFonts.lato(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        subtitle: Text(
          formattedDate,
          style: GoogleFonts.lato(
            color: Colors.grey[600],
            fontSize: 14,
          ),
        ),
        trailing: const Icon(
          FeatherIcons.chevronRight,
          size: 18,
          color: Colors.grey,
        ),
        onTap: () {
          // Navigate to exercise details or repeat the exercise
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'شما $technique را در $formattedDate تکمیل کردید',
                style: const TextStyle(fontFamily: 'Samim'),
              ),
              duration: const Duration(seconds: 2),
            ),
          );
        },
      ),
    );
  }
}
